import { DatabaseApiResponse, DatabaseTableConfig } from '@/types'

export const DATABASE_TABLES: DatabaseTableConfig[] = [
  {
    name: 'clients',
    displayName: 'Clients',
    description: 'Main client accounts with subscription and contact information',
  },
  {
    name: 'plans',
    displayName: 'Plans',
    description: 'Subscription plans with pricing and feature limits',
  },
  {
    name: 'client_subscriptions',
    displayName: 'Client Subscriptions',
    description: 'Client subscription history and billing information',
  },
  {
    name: 'client_credentials',
    displayName: 'Client Credentials',
    description: 'Social media platform credentials and connection details',
  },
  {
    name: 'chat_histories',
    displayName: 'Chat Histories',
    description: 'Complete conversation logs with RAG scoring and metadata',
  },
  {
    name: 'faqs',
    displayName: 'FAQs',
    description: 'Knowledge base with vector embeddings for semantic search',
  },
  {
    name: 'welcome_chat',
    displayName: 'Welcome Chat',
    description: 'Welcome messages with audio and photo support',
  },
  {
    name: 'photos',
    displayName: 'Photos',
    description: 'Photo storage with metadata and file paths',
  },
  {
    name: 'connected_acc',
    displayName: 'Connected Accounts',
    description: 'Tracks connected social media accounts per client',
  },
  {
    name: 'error_logs',
    displayName: 'Error Logs',
    description: 'Client-specific error tracking with platform context',
  },
  {
    name: 'error_trigger_logs',
    displayName: 'Error Trigger Logs',
    description: 'N8N workflow error monitoring and debugging',
  },
  {
    name: 'incoming_webhook_logs',
    displayName: 'Incoming Webhook Logs',
    description: 'Webhook event tracking for system monitoring',
  },
  {
    name: 'llm_requests',
    displayName: 'LLM Requests',
    description: 'LLM usage tracking with token counts and costs',
  },
  {
    name: 'model_pricing',
    displayName: 'Model Pricing',
    description: 'LLM model pricing configuration',
  },
]

// PostgreSQL column type definitions
export interface ColumnSchema {
  name: string
  type: string
  nullable: boolean
  isPrimaryKey: boolean
  isUnique: boolean
  hasDefault: boolean
  defaultValue?: string
  constraints?: string[]
  // Popup configuration for long content
  showInPopup?: boolean
  popupContentType?: 'text' | 'image' | 'file' | 'array'
}

// Table schema configuration for correct SQL generation and display
export const TABLE_SCHEMA_CONFIG: Record<string, {
  primaryKey: string
  orderByColumn: string
  hasCreatedAt: boolean
  columns: Record<string, ColumnSchema>
}> = {
  clients: {
    primaryKey: 'client_id',
    orderByColumn: 'created_at',
    hasCreatedAt: true,
    columns: {
      auth_id: { name: 'auth_id', type: 'uuid', nullable: false, isPrimaryKey: false, isUnique: true, hasDefault: false },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'CB + sequence' },
      username: { name: 'username', type: 'text', nullable: false, isPrimaryKey: false, isUnique: true, hasDefault: false },
      company_name: { name: 'company_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      contact_email: { name: 'contact_email', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      contact_phone: { name: 'contact_phone', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      status: { name: 'status', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'active' },
      created_by: { name: 'created_by', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      sector: { name: 'sector', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      lang: { name: 'lang', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      plan_type: { name: 'plan_type', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      billing_cycle: { name: 'billing_cycle', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      start_date: { name: 'start_date', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      next_billing_date: { name: 'next_billing_date', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      lang_2: { name: 'lang_2', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      usage_used: { name: 'usage_used', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      usage_limit: { name: 'usage_limit', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      contact_need: { name: 'contact_need', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  plans: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'integer', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'IDENTITY' },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      name: { name: 'name', type: 'text', nullable: false, isPrimaryKey: false, isUnique: true, hasDefault: false },
      price: { name: 'price', type: 'integer', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false, constraints: ['>=0'] },
      total_faqs: { name: 'total_faqs', type: 'integer', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false, constraints: ['>0'] },
      total_photos: { name: 'total_photos', type: 'integer', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false, constraints: ['>0'] },
      connections: { name: 'connections', type: 'integer', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false, constraints: ['>0'] },
      conv: { name: 'conv', type: 'integer', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false, constraints: ['>0'] },
    }
  },
  client_subscriptions: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      plan_type: { name: 'plan_type', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      billing_cycle: { name: 'billing_cycle', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      start_date: { name: 'start_date', type: 'date', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      next_billing_date: { name: 'next_billing_date', type: 'date', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  client_credentials: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_url: { name: 'fb_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_token: { name: 'fb_token', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_id: { name: 'fb_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: true, hasDefault: false },
      fb_name: { name: 'fb_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_url: { name: 'ig_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_token: { name: 'ig_token', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_id: { name: 'ig_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: true, hasDefault: false },
      ig_name: { name: 'ig_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_url: { name: 'tg_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_token: { name: 'tg_token', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_id: { name: 'tg_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: true, hasDefault: false },
      tg_name: { name: 'tg_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_id_name: { name: 'tg_id_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_biz_id: { name: 'tg_biz_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      wa_url: { name: 'wa_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      wa_token: { name: 'wa_token', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      wa_id: { name: 'wa_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: true, hasDefault: false },
      wa_name: { name: 'wa_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      web_url: { name: 'web_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      web_domain: { name: 'web_domain', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      web_name: { name: 'web_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_status: { name: 'tg_status', type: 'smallint', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      ig_status: { name: 'ig_status', type: 'smallint', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      fb_status: { name: 'fb_status', type: 'smallint', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      wa_status: { name: 'wa_status', type: 'smallint', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      web_status: { name: 'web_status', type: 'smallint', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      client_tg: { name: 'client_tg', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  chat_histories: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      customer_id: { name: 'customer_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      platform: { name: 'platform', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      user_message: { name: 'user_message', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      cleaned_question: { name: 'cleaned_question', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      bot_message: { name: 'bot_message', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      question: { name: 'question', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      answer: { name: 'answer', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      question_p: { name: 'question_p', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      answer_p: { name: 'answer_p', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      flag: { name: 'flag', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      rag_matched: { name: 'rag_matched', type: 'boolean', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      rag_score: { name: 'rag_score', type: 'real', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      chat_type: { name: 'chat_type', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      sub_flag: { name: 'sub_flag', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      lang: { name: 'lang', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      respond_lang: { name: 'respond_lang', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_name: { name: 'tg_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_id: { name: 'tg_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      audio_duration: { name: 'audio_duration', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      sector: { name: 'sector', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      button_clicked: { name: 'button_clicked', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      feedback_score: { name: 'feedback_score', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      photo_url: { name: 'photo_url', type: 'jsonb', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      answer_audio_duration: { name: 'answer_audio_duration', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  faqs: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      question: { name: 'question', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      question_p: { name: 'question_p', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      answer: { name: 'answer', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      answer_p: { name: 'answer_p', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      audio_url: { name: 'audio_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      photo_url: { name: 'photo_url', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'image' },
      photo_id: { name: 'photo_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      audio_duration: { name: 'audio_duration', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      audio_file_path: { name: 'audio_file_path', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      faq_id: { name: 'faq_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_photo_atmid: { name: 'fb_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_photo_atmid: { name: 'tg_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_photo_atmid: { name: 'ig_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_audio_atmid: { name: 'fb_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_audio_atmid: { name: 'tg_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_audio_atmid: { name: 'ig_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      is_visible: { name: 'is_visible', type: 'boolean', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      embedding: { name: 'embedding', type: 'vector(512)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  welcome_chat: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      answer: { name: 'answer', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      answer_p: { name: 'answer_p', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      audio_url: { name: 'audio_url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'text' },
      photo_url: { name: 'photo_url', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'image' },
      photo_id: { name: 'photo_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      audio_duration: { name: 'audio_duration', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      audio_file_path: { name: 'audio_file_path', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      chat_id: { name: 'chat_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_photo_atmid: { name: 'fb_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_photo_atmid: { name: 'tg_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_photo_atmid: { name: 'ig_photo_atmid', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      fb_audio_atmid: { name: 'fb_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_audio_atmid: { name: 'tg_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      ig_audio_atmid: { name: 'ig_audio_atmid', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  photos: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      photo_id: { name: 'photo_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      photo_url: { name: 'photo_url', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'image' },
      photo_file_path: { name: 'photo_file_path', type: 'text[]', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false, showInPopup: true, popupContentType: 'file' },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  connected_acc: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'bigserial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      platform: { name: 'platform', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      acc_id: { name: 'acc_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  error_logs: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      platform: { name: 'platform', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      customer_id: { name: 'customer_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_name: { name: 'tg_name', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      tg_id: { name: 'tg_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      flag: { name: 'flag', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      sector: { name: 'sector', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
      plan: { name: 'plan', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
    }
  },
  error_trigger_logs: {
    primaryKey: 'id',
    orderByColumn: 'timestamp',
    hasCreatedAt: false,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      workflow: { name: 'workflow', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      url: { name: 'url', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      node: { name: 'node', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      error_message: { name: 'error_message', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      timestamp: { name: 'timestamp', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  incoming_webhook_logs: {
    primaryKey: 'id',
    orderByColumn: 'id',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      platform: { name: 'platform', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      event_type: { name: 'event_type', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      customer_id: { name: 'customer_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      flag: { name: 'flag', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      chat_type: { name: 'chat_type', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      message: { name: 'message', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  llm_requests: {
    primaryKey: 'id',
    orderByColumn: 'created_at',
    hasCreatedAt: true,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      conversation_id: { name: 'conversation_id', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      client_id: { name: 'client_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      customer_id: { name: 'customer_id', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      platform: { name: 'platform', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      operation: { name: 'operation', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      task: { name: 'task', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      model_code: { name: 'model_code', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      model: { name: 'model', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      provider: { name: 'provider', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      input_tokens: { name: 'input_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      output_tokens: { name: 'output_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      cache_read_tokens: { name: 'cache_read_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      cache_write_tokens: { name: 'cache_write_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      thinking_tokens: { name: 'thinking_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      audio_input_tokens: { name: 'audio_input_tokens', type: 'integer', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      input_cost: { name: 'input_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      output_cost: { name: 'output_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      cache_read_cost: { name: 'cache_read_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      cache_write_cost: { name: 'cache_write_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      thinking_cost: { name: 'thinking_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      audio_input_cost: { name: 'audio_input_cost', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      total_cost: { name: 'total_cost', type: 'decimal(12,9)', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      flag: { name: 'flag', type: 'text', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      created_at: { name: 'created_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
  model_pricing: {
    primaryKey: 'id',
    orderByColumn: 'updated_at',
    hasCreatedAt: false,
    columns: {
      id: { name: 'id', type: 'serial', nullable: false, isPrimaryKey: true, isUnique: true, hasDefault: true, defaultValue: 'auto-increment' },
      name: { name: 'name', type: 'text', nullable: false, isPrimaryKey: false, isUnique: true, hasDefault: false },
      model: { name: 'model', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      provider: { name: 'provider', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      key: { name: 'key', type: 'text', nullable: false, isPrimaryKey: false, isUnique: false, hasDefault: false },
      input_cost_per_token: { name: 'input_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      output_cost_per_token: { name: 'output_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: false },
      cache_read_cost_per_token: { name: 'cache_read_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      cache_write_cost_per_token: { name: 'cache_write_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      thinking_cost_per_token: { name: 'thinking_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      audio_input_cost_per_token: { name: 'audio_input_cost_per_token', type: 'decimal(12,9)', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: '0' },
      updated_at: { name: 'updated_at', type: 'timestamp with time zone', nullable: true, isPrimaryKey: false, isUnique: false, hasDefault: true, defaultValue: 'now()' },
    }
  },
}

export async function executeDatabaseQuery(
  sql: string,
  params: any[] = []
): Promise<DatabaseApiResponse> {
  try {
    const response = await fetch('/api/database/', {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({ sql, params }),
    })

    const result = await response.json()
    return result
  } catch (error) {
    console.error('Database query error:', error)
    return {
      success: false,
      body: null,
      error_msg: 'Failed to execute database query',
    }
  }
}

export function getTableOrderByColumn(tableName: string): string {
  const config = TABLE_SCHEMA_CONFIG[tableName]
  if (!config) {
    console.warn(`No schema config found for table: ${tableName}`)
    return 'id' // fallback
  }
  return config.orderByColumn
}

export function buildTableQuery(tableName: string, limit: number, offset: number): string {
  const orderByColumn = getTableOrderByColumn(tableName)

  // For tables with vector columns, include them but handle serialization properly
  if (tableName === 'faqs') {
    return `SELECT id, client_id, question, question_p, answer, answer_p, audio_url, photo_url, photo_id, audio_duration, audio_file_path, updated_at, created_at, faq_id, fb_photo_atmid, fb_audio_atmid, tg_photo_atmid, tg_audio_atmid, ig_photo_atmid, ig_audio_atmid, is_visible, embedding FROM ${tableName} ORDER BY ${orderByColumn} DESC LIMIT $1 OFFSET $2`
  }

  return `SELECT * FROM ${tableName} ORDER BY ${orderByColumn} DESC LIMIT $1 OFFSET $2`
}

export async function fetchTableData(
  tableName: string,
  page: number = 1,
  limit: number = 50
): Promise<{ rows: Record<string, any>[], total: number }> {
  try {
    const offset = (page - 1) * limit
    
    // Fetch table data with correct ordering
    const dataQuery = buildTableQuery(tableName, limit, offset)
    const dataResult = await executeDatabaseQuery(dataQuery, [limit, offset])
    
    // Fetch total count
    const countQuery = `SELECT COUNT(*) as total FROM ${tableName}`
    const countResult = await executeDatabaseQuery(countQuery)
    
    if (dataResult.success && countResult.success) {
      return {
        rows: dataResult.body || [],
        total: countResult.body?.[0]?.total || 0,
      }
    }
    
    return { rows: [], total: 0 }
  } catch (error) {
    console.error('Error fetching table data:', error)
    return { rows: [], total: 0 }
  }
}

export async function fetchTableSchema(tableName: string): Promise<{
  columns: Array<{
    column_name: string
    data_type: string
    is_nullable: string
    column_default: string | null
  }>
}> {
  try {
    const schemaQuery = `
      SELECT 
        column_name,
        data_type,
        is_nullable,
        column_default
      FROM information_schema.columns 
      WHERE table_name = $1 
      ORDER BY ordinal_position
    `
    
    const result = await executeDatabaseQuery(schemaQuery, [tableName])
    
    if (result.success) {
      const columns = (result.body || []) as Array<{
        column_name: string
        data_type: string
        is_nullable: string
        column_default: string | null
      }>
      return { columns }
    }
    
    return { columns: [] }
  } catch (error) {
    console.error('Error fetching table schema:', error)
    return { columns: [] }
  }
}

export function formatCellValue(value: any): string {
  if (value === null || value === undefined) {
    return 'NULL'
  }
  if (typeof value === 'object') {
    return JSON.stringify(value)
  }
  if (typeof value === 'boolean') {
    return value ? 'true' : 'false'
  }
  if (typeof value === 'string' && value.length > 100) {
    return value.substring(0, 100) + '...'
  }
  return String(value)
}

export function getTableConfigByName(tableName: string): DatabaseTableConfig | undefined {
  return DATABASE_TABLES.find(table => table.name === tableName)
}

export function isValidTableName(tableName: string): boolean {
  return DATABASE_TABLES.some(table => table.name === tableName)
}

export function sanitizeTableName(tableName: string): string {
  // Basic sanitization to prevent SQL injection
  return tableName.replace(/[^a-zA-Z0-9_]/g, '')
}

export function formatTimestamp(timestamp: string): string {
  try {
    const date = new Date(timestamp)
    // Format as GMT+7 (Asia/Bangkok timezone)
    return date.toLocaleString('en-US', {
      timeZone: 'Asia/Bangkok',
      year: 'numeric',
      month: '2-digit', 
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit',
      second: '2-digit',
      hour12: false
    })
  } catch (error) {
    return timestamp
  }
}

export function formatDate(dateString: string): string {
  try {
    // Handle PostgreSQL DATE fields that come as "2024-07-14T00:00:00.000Z"
    if (typeof dateString === 'string') {
      // Extract date part from PostgreSQL DATE format: "2024-07-14T00:00:00.000Z" → "2024-07-14"
      const isoDateMatch = dateString.match(/^(\d{4}-\d{2}-\d{2})T00:00:00\.000Z$/)
      if (isoDateMatch) {
        const [year, month, day] = isoDateMatch[1].split('-')
        return `${month}/${day}/${year}`
      }
      
      // Handle pure DATE strings like "2024-07-13"
      const pureDateMatch = dateString.match(/^\d{4}-\d{2}-\d{2}$/)
      if (pureDateMatch) {
        const [year, month, day] = dateString.split('-')
        return `${month}/${day}/${year}`
      }
    }
    
    // Fallback for other date formats
    const date = new Date(dateString)
    return date.toLocaleDateString('en-US', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit'
    })
  } catch (error) {
    return dateString
  }
}

export function getColumnDisplayName(columnName: string): string {
  // Convert snake_case to Title Case
  return columnName
    .split('_')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ')
}