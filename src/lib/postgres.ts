import { Pool, PoolClient } from 'pg'

// Global connection pool
let pool: Pool | null = null

export function createPostgresPool(): Pool {
  if (!pool) {
    // Validate required environment variables
    const requiredEnvVars = [
      'POSTGRES_HOST',
      'POSTGRES_PORT',
      'POSTGRES_USER',
      'POSTGRES_PASSWORD',
      'POSTGRES_DATABASE'
    ]

    for (const envVar of requiredEnvVars) {
      if (!process.env[envVar]) {
        throw new Error(`Missing required environment variable: ${envVar}`)
      }
    }

    pool = new Pool({
      host: process.env.POSTGRES_HOST,
      port: parseInt(process.env.POSTGRES_PORT || '5432'),
      user: process.env.POSTGRES_USER,
      password: process.env.POSTGRES_PASSWORD,
      database: process.env.POSTGRES_DATABASE,
      ssl: process.env.POSTGRES_SSL === 'require' ? { rejectUnauthorized: false } : false,
      max: 10, // Maximum number of clients in the pool
      idleTimeoutMillis: 30000, // Close idle clients after 30 seconds
      connectionTimeoutMillis: 10000, // Return error after 10 seconds if connection could not be established
      query_timeout: 30000, // Query timeout 30 seconds
      statement_timeout: 30000, // Statement timeout 30 seconds
    })

    // Handle pool errors
    pool.on('error', (err) => {
      console.error('Unexpected error on idle PostgreSQL client:', err)
    })
  }

  return pool
}

export async function executeQuery<T = any>(
  sql: string,
  params: any[] = []
): Promise<T[]> {
  const pool = createPostgresPool()
  let client: PoolClient | null = null

  try {
    client = await pool.connect()
    const result = await client.query(sql, params)
    return result.rows
  } catch (error) {
    console.error('PostgreSQL query error:', error)
    throw error
  } finally {
    if (client) {
      client.release()
    }
  }
}

export async function testConnection(): Promise<{ success: boolean; message: string; details?: any }> {
  try {
    console.log('Testing PostgreSQL connection...')
    console.log('Host:', process.env.POSTGRES_HOST)
    console.log('Port:', process.env.POSTGRES_PORT)
    console.log('User:', process.env.POSTGRES_USER)
    console.log('Database:', process.env.POSTGRES_DATABASE)
    console.log('SSL:', process.env.POSTGRES_SSL)
    
    const result = await executeQuery('SELECT NOW() as current_time, version() as pg_version')
    return {
      success: true,
      message: `Connected successfully. PostgreSQL version: ${result[0].pg_version}`
    }
  } catch (error) {
    console.error('PostgreSQL connection test failed:', error)
    return {
      success: false,
      message: `Connection failed: ${error instanceof Error ? error.message : 'Unknown error'}`,
      details: error
    }
  }
}

// Cleanup function for graceful shutdown
export async function closePool(): Promise<void> {
  if (pool) {
    await pool.end()
    pool = null
  }
}