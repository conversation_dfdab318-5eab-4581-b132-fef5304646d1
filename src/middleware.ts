import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { verifyAuth } from '@/utils/auth'

export async function middleware(request: NextRequest) {
  console.log(`[MIDDLEWARE] 🔍 Checking auth for: ${request.nextUrl.pathname}`)

  // Use our optimized authentication
  const authResult = await verifyAuth(request)

  // If user is not authenticated
  if (!authResult.authenticated) {
    const totalTime = authResult.timings?.total || 0
    console.log(`[MIDDLEWARE] ❌ No user found (${authResult.method} took ${totalTime}ms / ${(totalTime/1000).toFixed(3)}s)`)
    // Allow access to login page
    if (request.nextUrl.pathname === '/login') {
      return NextResponse.next()
    }

    // Redirect to login for all other routes
    console.log(`[MIDDLEWARE] 🔄 Redirecting to login`)
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Check if user has admin role
  if (!authResult.isAdmin) {
    const totalTime = authResult.timings?.total || 0
    console.log(`[MIDDLEWARE] ❌ User ${authResult.user?.email} not admin - took ${totalTime}ms / ${(totalTime/1000).toFixed(3)}s`)
    // If not admin, redirect to login regardless of current route
    return NextResponse.redirect(new URL('/login', request.url))
  }

  const totalTime = authResult.timings?.total || 0
  console.log(`[MIDDLEWARE] ✅ Admin user ${authResult.user?.email} authenticated - took ${totalTime}ms / ${(totalTime/1000).toFixed(3)}s (${authResult.method})`)

  // If user is authenticated admin and trying to access login page
  if (authResult.user && request.nextUrl.pathname === '/login') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  // If user is authenticated admin and trying to access root, redirect to users
  if (authResult.user && request.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  return NextResponse.next()
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}