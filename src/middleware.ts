import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createMiddlewareSupabaseClient } from '@/lib/supabase'

export async function middleware(request: NextRequest) {
  const { supabase, response } = createMiddlewareSupabaseClient(request)

  // Get claims (faster than getUser for asymmetric JWTs)
  const { data: claimsData, error } = await supabase.auth.getClaims()

  // If no valid claims/session
  if (error || !claimsData?.claims) {
    // Allow access to login page
    if (request.nextUrl.pathname === '/login') {
      return response
    }

    // Redirect to login for all other routes
    return NextResponse.redirect(new URL('/login', request.url))
  }

  const claims = claimsData.claims

  // Check if user has admin role in metadata
  const userRole = (claims as any).app_metadata?.role
  const adminEmail = process.env.ADMIN_USER
  const isAdmin = userRole === 'admin' || (adminEmail && claims.email === adminEmail)

  if (!isAdmin) {
    // If not admin, redirect to login regardless of current route
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // If user is authenticated admin and trying to access login page
  if (claims && request.nextUrl.pathname === '/login') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  // If user is authenticated admin and trying to access root, redirect to users
  if (claims && request.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}