import { NextResponse } from 'next/server'
import type { NextRequest } from 'next/server'
import { createMiddlewareSupabaseClient } from '@/lib/supabase'

export async function middleware(request: NextRequest) {
  const startTime = Date.now()
  const { supabase, response } = createMiddlewareSupabaseClient(request)

  console.log(`[MIDDLEWARE] 🔍 Checking auth for: ${request.nextUrl.pathname}`)

  // Get the current user
  const getUserStart = Date.now()
  const { data: { user } } = await supabase.auth.getUser()
  const getUserTime = Date.now() - getUserStart

  // If user is not authenticated
  if (!user) {
    console.log(`[MIDDLEWARE] ❌ No user found (getUser took ${getUserTime}ms)`)
    // Allow access to login page
    if (request.nextUrl.pathname === '/login') {
      return response
    }

    // Redirect to login for all other routes
    console.log(`[MIDDLEWARE] 🔄 Redirecting to login`)
    return NextResponse.redirect(new URL('/login', request.url))
  }

  // Check if user has admin role in metadata
  const userRole = user.app_metadata?.role
  const totalTime = Date.now() - startTime

  if (userRole !== 'admin') {
    console.log(`[MIDDLEWARE] ❌ User ${user.email} not admin (role: ${userRole}) - took ${totalTime}ms`)
    // If not admin, redirect to login regardless of current route
    return NextResponse.redirect(new URL('/login', request.url))
  }

  console.log(`[MIDDLEWARE] ✅ Admin user ${user.email} authenticated - took ${totalTime}ms (getUser: ${getUserTime}ms)`)

  // If user is authenticated admin and trying to access login page
  if (user && request.nextUrl.pathname === '/login') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  // If user is authenticated admin and trying to access root, redirect to users
  if (user && request.nextUrl.pathname === '/') {
    return NextResponse.redirect(new URL('/users', request.url))
  }

  return response
}

export const config = {
  matcher: [
    /*
     * Match all request paths except for the ones starting with:
     * - _next/static (static files)
     * - _next/image (image optimization files)
     * - favicon.ico (favicon file)
     * - public (public files)
     */
    '/((?!_next/static|_next/image|favicon.ico|public).*)',
  ],
}