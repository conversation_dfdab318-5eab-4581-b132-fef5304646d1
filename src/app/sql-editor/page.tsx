'use client'

import { useState, useRef, useCallback } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Textarea } from '@/components/ui/textarea'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { toast } from 'sonner'
import { Play, FileText, Copy, Download, ChevronLeft, ChevronRight } from 'lucide-react'

interface QueryResult {
  success: boolean
  data?: any[]
  error?: string
  executionTime?: number
}

export default function SQLEditorPage() {
  const [sql, setSql] = useState('')
  const [result, setResult] = useState<QueryResult | null>(null)
  const [loading, setLoading] = useState(false)
  const [rowsPerPage, setRowsPerPage] = useState(10)
  const [currentPage, setCurrentPage] = useState(0)
  const textareaRef = useRef<HTMLTextAreaElement>(null)

  const filterSQL = (sqlText: string): string => {
    return sqlText
      .split('\n')
      .map(line => {
        // Handle inline comments: remove everything after --
        const commentIndex = line.indexOf('--')
        if (commentIndex !== -1) {
          return line.substring(0, commentIndex).trim()
        }
        return line.trim()
      })
      .filter(line => line.length > 0) // Remove empty lines
      .join('\n')
      .trim()
  }

  const executeSQL = async () => {
    if (!sql.trim()) {
      toast.error('Please enter a SQL query')
      return
    }

    // Filter out comments and empty lines
    const filteredSQL = filterSQL(sql)
    if (!filteredSQL) {
      toast.error('No executable SQL found (only comments detected)')
      return
    }

    setLoading(true)
    try {
      const response = await fetch('/api/sql-execute', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ sql: filteredSQL }),
      })

      const result = await response.json()
      setResult(result)

      if (result.success) {
        setCurrentPage(0) // Reset to first page on new query
        toast.success(`Query executed successfully in ${result.executionTime || 0}ms`)
      } else {
        toast.error(result.error || 'Failed to execute query')
      }
    } catch (error) {
      const errorResult = { success: false, error: 'Network error occurred' }
      setResult(errorResult)
      toast.error('Network error occurred')
    } finally {
      setLoading(false)
    }
  }

  const toggleComment = useCallback(() => {
    if (!textareaRef.current) return

    const textarea = textareaRef.current
    const start = textarea.selectionStart
    const end = textarea.selectionEnd
    const text = textarea.value

    // Get the lines that are selected
    const beforeSelection = text.substring(0, start)
    const selection = text.substring(start, end)
    const afterSelection = text.substring(end)

    const lines = selection.split('\n')
    const lineStartIndex = beforeSelection.lastIndexOf('\n') + 1
    const firstLine = beforeSelection.substring(lineStartIndex)

    // Check if the first line or selection is commented
    const isCommented = lines.every(line => line.trim().startsWith('--') || line.trim() === '')

    let newSelection: string
    let newStart: number
    let newEnd: number

    if (isCommented) {
      // Uncomment: remove "-- " from the beginning of each line
      newSelection = lines.map(line => {
        if (line.trim().startsWith('--')) {
          return line.replace(/^(\s*)--\s?/, '$1')
        }
        return line
      }).join('\n')
      
      // Calculate new cursor position
      const removedChars = selection.length - newSelection.length
      newStart = start
      newEnd = end - removedChars
    } else {
      // Comment: add "-- " to the beginning of each line
      newSelection = lines.map(line => {
        if (line.trim() === '') return line
        const indent = line.match(/^\s*/)?.[0] || ''
        const content = line.substring(indent.length)
        return `${indent}-- ${content}`
      }).join('\n')
      
      // Calculate new cursor position
      const addedChars = newSelection.length - selection.length
      newStart = start
      newEnd = end + addedChars
    }

    // Update the textarea value
    const newText = beforeSelection + newSelection + afterSelection
    setSql(newText)

    // Restore cursor position after React re-renders
    setTimeout(() => {
      if (textareaRef.current) {
        textareaRef.current.focus()
        textareaRef.current.setSelectionRange(newStart, newEnd)
      }
    }, 0)
  }, [])

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    const isMac = navigator.platform.toUpperCase().indexOf('MAC') >= 0
    const cmdOrCtrl = isMac ? e.metaKey : e.ctrlKey

    if (cmdOrCtrl && e.key === 'Enter') {
      e.preventDefault()
      executeSQL()
    } else if (cmdOrCtrl && e.key === 'l') {
      e.preventDefault()
      toggleComment()
    }
  }

  const copyResults = () => {
    if (!result?.data || result.data.length === 0) return
    
    const jsonStr = JSON.stringify(result.data, null, 2)
    navigator.clipboard.writeText(jsonStr)
    toast.success('Results copied to clipboard')
  }

  const downloadResults = () => {
    if (!result?.data || result.data.length === 0) return
    
    const jsonStr = JSON.stringify(result.data, null, 2)
    const blob = new Blob([jsonStr], { type: 'application/json' })
    const url = URL.createObjectURL(blob)
    const a = document.createElement('a')
    a.href = url
    a.download = `sql_results_${new Date().toISOString().slice(0, 19).replace(/:/g, '-')}.json`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
    toast.success('Results downloaded')
  }

  const renderResults = () => {
    if (!result) return null

    if (!result.success) {
      return (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-red-600 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Error
            </CardTitle>
          </CardHeader>
          <CardContent>
            <pre className="text-sm text-red-600 whitespace-pre-wrap bg-red-50 dark:bg-red-950 p-4 rounded border font-mono">
              {JSON.stringify(result, null, 2)}
            </pre>
          </CardContent>
        </Card>
      )
    }

    if (!result.data || result.data.length === 0) {
      return (
        <Card className="mt-6">
          <CardHeader>
            <CardTitle className="text-green-600 flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Success ({result.executionTime}ms)
            </CardTitle>
          </CardHeader>
          <CardContent>
            <p className="text-muted-foreground">Query executed successfully. No rows returned.</p>
          </CardContent>
        </Card>
      )
    }

    const columns = Object.keys(result.data[0])
    const totalPages = Math.ceil(result.data.length / rowsPerPage)
    const startRow = currentPage * rowsPerPage
    const endRow = Math.min(startRow + rowsPerPage, result.data.length)
    const paginatedData = result.data.slice(startRow, endRow)

    return (
      <Card className="mt-6">
        <CardHeader>
          <CardTitle className="text-green-600 flex items-center justify-between">
            <div className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Results ({result.data.length} rows, {columns.length} columns) • {result.executionTime}ms
            </div>
            <div className="flex items-center space-x-2">
              <div className="flex items-center space-x-2">
                <span className="text-sm text-muted-foreground">Rows per page:</span>
                <Select
                  value={rowsPerPage.toString()}
                  onValueChange={(value) => {
                    setRowsPerPage(parseInt(value))
                    setCurrentPage(0) // Reset to first page
                  }}
                >
                  <SelectTrigger className="w-20 h-8">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="10">10</SelectItem>
                    <SelectItem value="20">20</SelectItem>
                    <SelectItem value="50">50</SelectItem>
                    <SelectItem value="100">100</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <Button
                variant="outline"
                size="sm"
                onClick={copyResults}
                className="flex items-center"
              >
                <Copy className="h-4 w-4 mr-1" />
                Copy JSON
              </Button>
              <Button
                variant="outline"
                size="sm"
                onClick={downloadResults}
                className="flex items-center"
              >
                <Download className="h-4 w-4 mr-1" />
                Download
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {/* JSON view with pagination */}
            <div>
              <h4 className="text-sm font-semibold mb-2">
                JSON Output (rows {startRow + 1}-{endRow} of {result.data.length}):
              </h4>
              <pre className="text-xs bg-gray-50 dark:bg-gray-900 p-4 rounded border overflow-x-auto font-mono max-h-96 overflow-y-auto">
                {JSON.stringify(paginatedData, null, 2)}
              </pre>
            </div>

            {/* Pagination controls */}
            {totalPages > 1 && (
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage - 1)}
                    disabled={currentPage === 0}
                    className="flex items-center"
                  >
                    <ChevronLeft className="h-4 w-4 mr-1" />
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setCurrentPage(currentPage + 1)}
                    disabled={currentPage === totalPages - 1}
                    className="flex items-center"
                  >
                    Next
                    <ChevronRight className="h-4 w-4 ml-1" />
                  </Button>
                </div>
                <span className="text-sm text-muted-foreground">
                  Page {currentPage + 1} of {totalPages}
                </span>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold">SQL Editor</h1>
          <p className="text-muted-foreground">Execute PostgreSQL queries directly</p>
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            Query Editor
            <div className="flex items-center space-x-2">
              <span className="text-sm text-muted-foreground">
                Cmd+Enter to execute • Cmd+L to comment
              </span>
              <Button
                onClick={executeSQL}
                disabled={loading || !sql.trim()}
                className="flex items-center"
              >
                <Play className="h-4 w-4 mr-2" />
                {loading ? 'Executing...' : 'Execute'}
              </Button>
            </div>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Textarea
            ref={textareaRef}
            value={sql}
            onChange={(e) => setSql(e.target.value)}
            onKeyDown={handleKeyDown}
            placeholder="Enter your PostgreSQL query here...

Example:
SELECT * FROM users LIMIT 10;"
            className="min-h-[300px] font-mono text-sm"
            disabled={loading}
          />
        </CardContent>
      </Card>

      {renderResults()}
    </div>
  )
}