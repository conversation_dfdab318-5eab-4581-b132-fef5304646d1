import { createClient } from '@supabase/supabase-js'
import { NextResponse } from 'next/server'
import { verifyAdminAuth } from '@/utils/auth'
import { executeQuery } from '@/lib/postgres'

export async function POST(request: Request) {
  try {
    // Verify admin authentication
    const { authenticated, isAdmin } = await verifyAdminAuth(request as any)
    if (!authenticated || !isAdmin) {
      return NextResponse.json(
        { error: 'Unauthorized' },
        { status: 403 }
      )
    }

    // Use service role client for admin operations
    const adminClient = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.SUPABASE_SERVICE_ROLE_KEY!
    )

    const body = await request.json()
    const { action, email, userId, password } = body

    if (action === 'find_user') {
      if (!email) {
        return NextResponse.json(
          { error: 'Email is required' },
          { status: 400 }
        )
      }

      // Extract username from email
      const username = email.split('@')[0]

      try {
        // Find user in clients table by username using direct PostgreSQL
        const sql = 'SELECT auth_id, client_id FROM clients WHERE username = $1'
        const params = [username]
        
        const clientData = await executeQuery(sql, params)

        if (!clientData || clientData.length === 0) {
          return NextResponse.json(
            { error: `No user found with username: ${username}` },
            { status: 404 }
          )
        }

        const clientUser = clientData[0]
        
        // Return the auth_id for Supabase auth operations (still needed for password reset)
        return NextResponse.json({
          userId: clientUser.auth_id,
          clientId: clientUser.client_id
        })
      } catch (lookupError) {
        console.error('Error during user lookup:', lookupError)
        return NextResponse.json(
          { error: 'Error finding user' },
          { status: 500 }
        )
      }
    }

    if (action === 'update_password') {
      if (!userId || !password) {
        return NextResponse.json(
          { error: 'User ID and password are required' },
          { status: 400 }
        )
      }

      try {
        // Update auth user password using admin client
        const { error: updateError } = await adminClient.auth.admin.updateUserById(
          userId,
          { password: password }
        )

        if (updateError) {
          console.error('Error updating auth user password:', updateError)
          return NextResponse.json(
            { error: 'Failed to update password: ' + updateError.message },
            { status: 500 }
          )
        }

        return NextResponse.json({
          success: true,
          message: 'Password updated successfully'
        })
      } catch (error) {
        console.error('Error updating password:', error)
        return NextResponse.json(
          { error: 'Failed to update password: ' + (error instanceof Error ? error.message : 'Unknown error') },
          { status: 500 }
        )
      }
    }

    return NextResponse.json(
      { error: 'Invalid action' },
      { status: 400 }
    )
  } catch (error) {
    console.error('Server error:', error)
    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    )
  }
}