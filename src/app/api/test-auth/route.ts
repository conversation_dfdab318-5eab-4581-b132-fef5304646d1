import { NextRequest, NextResponse } from 'next/server'
import { verifyAuth } from '@/lib/auth-optimized'

export async function GET(request: NextRequest) {
  try {
    const result = await verifyAuth(request)
    
    return NextResponse.json({
      success: true,
      authenticated: result.authenticated,
      isAdmin: result.isAdmin,
      method: result.method,
      timings: result.timings,
      user: result.user ? {
        id: result.user.id,
        email: result.user.email,
        role: result.user.app_metadata?.role
      } : null,
      diagnostics: {
        hasAuthHeader: !!request.headers.get('authorization'),
        hasCookies: request.cookies.size > 0,
        environment: {
          hasSupabaseUrl: !!process.env.NEXT_PUBLIC_SUPABASE_URL,
          hasPublishableKey: !!process.env.NEXT_PUBLIC_SUPABASE_PUBLISHABLE_KEY,
          hasSecretKey: !!process.env.SUPABASE_SECRET_KEY,
          hasAdminUser: !!process.env.ADMIN_USER
        }
      }
    })
  } catch (error) {
    console.error('Test auth error:', error)
    return NextResponse.json(
      { 
        success: false, 
        error: 'Authentication test failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    )
  }
}
