import { NextRequest, NextResponse } from 'next/server'
import { executeQuery } from '@/lib/postgres'

// Whitelist of allowed tables for security
const ALLOWED_TABLES = [
  'clients',
  'plans',
  'client_subscriptions',
  'client_credentials',
  'chat_histories',
  'faqs',
  'welcome_chat',
  'photos',
  'connected_acc',
  'error_logs',
  'error_trigger_logs',
  'incoming_webhook_logs',
  'llm_requests',
  'model_pricing'
]

// Whitelist of allowed sort columns to prevent SQL injection
const ALLOWED_SORT_COLUMNS = [
  'id', 'name', 'email', 'created_at', 'updated_at', 'status', 'title', 
  'type', 'date', 'timestamp', 'user_id', 'client_id', 'plan_id'
]

interface PaginationParams {
  page: number
  limit: number
  sort: string
  order: 'asc' | 'desc'
  search?: string
  filter?: Record<string, any>
}

function validateTableName(tableName: string): boolean {
  return ALLOWED_TABLES.includes(tableName)
}

function validateSortColumn(column: string): boolean {
  return ALLOWED_SORT_COLUMNS.includes(column)
}

function buildWhereClause(filters: Record<string, any>, search?: string): { clause: string, params: any[] } {
  const conditions: string[] = []
  const params: any[] = []
  let paramIndex = 1

  // Add filter conditions
  for (const [column, value] of Object.entries(filters)) {
    if (value !== null && value !== undefined && value !== '') {
      // Validate column name to prevent SQL injection
      if (!/^[a-zA-Z_][a-zA-Z0-9_]*$/.test(column)) {
        continue // Skip invalid column names
      }
      conditions.push(`${column} = $${paramIndex}`)
      params.push(value)
      paramIndex++
    }
  }

  // Add search condition (basic implementation)
  if (search && search.trim()) {
    // This is a simplified search - in production you'd want to specify searchable columns
    conditions.push(`(
      CAST(id AS TEXT) ILIKE $${paramIndex} OR
      CASE 
        WHEN column_name IN ('name', 'title', 'email', 'type', 'status') 
        THEN CAST(column_name AS TEXT) ILIKE $${paramIndex}
        ELSE FALSE 
      END
    )`)
    params.push(`%${search}%`)
    paramIndex++
  }

  return {
    clause: conditions.length > 0 ? `WHERE ${conditions.join(' AND ')}` : '',
    params
  }
}

// GET - Fetch table data with pagination
export async function GET(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  const startTime = Date.now()
  
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    // Parse query parameters
    const searchParams = request.nextUrl.searchParams
    const page = Math.max(1, parseInt(searchParams.get('page') || '1'))
    const limit = Math.min(500, Math.max(1, parseInt(searchParams.get('limit') || '20')))
    const sort = searchParams.get('sort') || ''
    const order = (searchParams.get('order') || 'asc').toLowerCase() === 'desc' ? 'desc' : 'asc'
    const search = searchParams.get('search') || undefined
    const filterParam = searchParams.get('filter')
    
    let filter: Record<string, any> = {}
    if (filterParam) {
      try {
        filter = JSON.parse(filterParam)
      } catch (e) {
        return NextResponse.json({
          success: false,
          error: 'Invalid filter format. Must be valid JSON.',
          code: 'INVALID_FILTER'
        }, { status: 400 })
      }
    }

    // Get table primary key and first column for sorting
    let sortColumn = 'id' // default fallback
    
    // Special case for clients table - use client_id for sorting to match frontend expectations
    if (tableName === 'clients') {
      sortColumn = sort || 'client_id'
    } else {
      try {
        // Get primary key column
        const pkQuery = `
          SELECT kcu.column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = $1
          ORDER BY kcu.ordinal_position
          LIMIT 1
        `
        const pkResult = await executeQuery(pkQuery, [tableName])
        
        if (pkResult.length > 0) {
          const primaryKey = pkResult[0].column_name
          // Use requested sort column if provided, otherwise use primary key
          sortColumn = sort || primaryKey
        } else {
          // No primary key found, get first column
          const columnsQuery = `
            SELECT column_name 
            FROM information_schema.columns 
            WHERE table_schema = 'public' AND table_name = $1
            ORDER BY ordinal_position
            LIMIT 1
          `
          const columnsResult = await executeQuery(columnsQuery, [tableName])
          if (columnsResult.length > 0) {
            sortColumn = sort || columnsResult[0].column_name
          }
        }
      } catch (error) {
        console.error('Error getting table structure:', error)
        // If we can't get columns, just use requested sort or fallback
        sortColumn = sort || 'id'
      }
    }
    
    // Build WHERE clause
    const { clause: whereClause, params: whereParams } = buildWhereClause(filter, search)
    
    // Calculate offset
    const offset = (page - 1) * limit
    
    // Build and execute main query
    const mainQuery = `
      SELECT * FROM ${tableName}
      ${whereClause}
      ORDER BY ${sortColumn} ${order.toUpperCase()}
      LIMIT $${whereParams.length + 1} OFFSET $${whereParams.length + 2}
    `
    const mainParams = [...whereParams, limit, offset]
    
    // Build and execute count query
    const countQuery = `SELECT COUNT(*) as total FROM ${tableName} ${whereClause}`
    const countParams = whereParams
    
    // Execute both queries
    const [data, countResult] = await Promise.all([
      executeQuery(mainQuery, mainParams),
      executeQuery(countQuery, countParams)
    ])
    
    const total = parseInt(countResult[0]?.total || '0')
    const totalPages = Math.ceil(total / limit)
    const executionTime = Date.now() - startTime

    return NextResponse.json({
      success: true,
      data,
      pagination: {
        page,
        limit,
        total,
        totalPages
      },
      executionTime
    })

  } catch (error) {
    console.error('Table GET error:', error)
    const executionTime = Date.now() - startTime
    
    return NextResponse.json({
      success: false,
      error: 'Failed to fetch table data',
      details: error instanceof Error ? error.message : 'Unknown error',
      executionTime
    }, { status: 500 })
  }
}

// POST - Insert new row
export async function POST(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    const body = await request.json()
    const { data } = body

    if (!data || typeof data !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Invalid request body. Expected { data: {...} }',
        code: 'INVALID_REQUEST_BODY'
      }, { status: 400 })
    }

    // Build INSERT query
    const columns = Object.keys(data).filter(key => 
      /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(key) // Validate column names
    )
    const values = columns.map(col => data[col])
    const placeholders = columns.map((_, index) => `$${index + 1}`).join(', ')
    
    if (columns.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No valid columns provided',
        code: 'NO_VALID_COLUMNS'
      }, { status: 400 })
    }

    const insertQuery = `
      INSERT INTO ${tableName} (${columns.join(', ')})
      VALUES (${placeholders})
      RETURNING *
    `

    const result = await executeQuery(insertQuery, values)
    
    if (result.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Insert operation failed',
        code: 'INSERT_FAILED'
      }, { status: 500 })
    }

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'Row inserted successfully'
    })

  } catch (error) {
    console.error('Table POST error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to insert row',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// PUT - Update existing row
export async function PUT(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    const body = await request.json()
    const { id, data } = body

    if (!id || !data || typeof data !== 'object') {
      return NextResponse.json({
        success: false,
        error: 'Invalid request body. Expected { id: ..., data: {...} }',
        code: 'INVALID_REQUEST_BODY'
      }, { status: 400 })
    }

    // Build UPDATE query
    const columns = Object.keys(data).filter(key => 
      /^[a-zA-Z_][a-zA-Z0-9_]*$/.test(key) // Validate column names
    )
    const values = columns.map(col => data[col])
    
    if (columns.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No valid columns provided for update',
        code: 'NO_VALID_COLUMNS'
      }, { status: 400 })
    }

    // Get primary key column for WHERE clause
    let primaryKeyColumn = 'id' // fallback
    
    // Special case for clients table - use client_id instead of auth_id
    // since frontend sends client_id values
    if (tableName === 'clients') {
      primaryKeyColumn = 'client_id'
    } else {
      try {
        const pkQuery = `
          SELECT kcu.column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = $1
          ORDER BY kcu.ordinal_position
          LIMIT 1
        `
        const pkResult = await executeQuery(pkQuery, [tableName])
        if (pkResult.length > 0) {
          primaryKeyColumn = pkResult[0].column_name
        }
      } catch (error) {
        console.error('Error getting primary key for update:', error)
      }
    }

    const setClause = columns.map((col, index) => `${col} = $${index + 1}`).join(', ')
    const updateQuery = `
      UPDATE ${tableName}
      SET ${setClause}
      WHERE ${primaryKeyColumn} = $${columns.length + 1}
      RETURNING *
    `

    const result = await executeQuery(updateQuery, [...values, id])
    
    if (result.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'No row found with the specified ID',
        code: 'ROW_NOT_FOUND'
      }, { status: 404 })
    }

    return NextResponse.json({
      success: true,
      data: result[0],
      message: 'Row updated successfully'
    })

  } catch (error) {
    console.error('Table PUT error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to update row',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}

// DELETE - Delete row(s)
export async function DELETE(
  request: NextRequest,
  { params }: { params: Promise<{ tableName: string }> }
) {
  try {
    const { tableName } = await params
    
    // Validate table name
    if (!validateTableName(tableName)) {
      return NextResponse.json({
        success: false,
        error: `Table '${tableName}' is not allowed`,
        code: 'TABLE_NOT_ALLOWED'
      }, { status: 400 })
    }

    const body = await request.json()
    const { ids } = body

    if (!ids || !Array.isArray(ids) || ids.length === 0) {
      return NextResponse.json({
        success: false,
        error: 'Invalid request body. Expected { ids: [...] }',
        code: 'INVALID_REQUEST_BODY'
      }, { status: 400 })
    }

    // Get primary key column for WHERE clause
    let primaryKeyColumn = 'id' // fallback
    
    // Special case for clients table - use client_id instead of auth_id
    // since frontend sends client_id values
    if (tableName === 'clients') {
      primaryKeyColumn = 'client_id'
    } else {
      try {
        const pkQuery = `
          SELECT kcu.column_name
          FROM information_schema.table_constraints tc
          JOIN information_schema.key_column_usage kcu 
            ON tc.constraint_name = kcu.constraint_name
            AND tc.table_schema = kcu.table_schema
          WHERE tc.constraint_type = 'PRIMARY KEY'
            AND tc.table_schema = 'public'
            AND tc.table_name = $1
          ORDER BY kcu.ordinal_position
          LIMIT 1
        `
        const pkResult = await executeQuery(pkQuery, [tableName])
        if (pkResult.length > 0) {
          primaryKeyColumn = pkResult[0].column_name
        }
      } catch (error) {
        console.error('Error getting primary key for delete:', error)
      }
    }

    // Build DELETE query with parameterized placeholders
    const placeholders = ids.map((_, index) => `$${index + 1}`).join(', ')
    const deleteQuery = `
      DELETE FROM ${tableName}
      WHERE ${primaryKeyColumn} IN (${placeholders})
    `

    const result = await executeQuery(deleteQuery, ids)
    
    // PostgreSQL returns the number of affected rows in result.rowCount
    // But since we're using our executeQuery wrapper, we need to count differently
    const deletedCount = ids.length // Approximate - in production you'd want to verify

    return NextResponse.json({
      success: true,
      deletedCount,
      message: `${deletedCount} row(s) deleted successfully`
    })

  } catch (error) {
    console.error('Table DELETE error:', error)
    
    return NextResponse.json({
      success: false,
      error: 'Failed to delete row(s)',
      details: error instanceof Error ? error.message : 'Unknown error'
    }, { status: 500 })
  }
}