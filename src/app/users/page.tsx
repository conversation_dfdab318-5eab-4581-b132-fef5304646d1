'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { ResetPasswordForm } from '@/components/forms/reset-password-form'
import { PlanUpdateConfirmation, AddMessageConfirmation } from '@/components/ui/confirmation-dialog'
import { toast } from 'sonner'
import { MessageSquare, Crown, KeyRound, User } from 'lucide-react'
import { formatTimestamp, formatDate } from '@/lib/database'

export default function UsersPage() {
  const [updatePlan, setUpdatePlan] = useState({
    username: '',
    plan: '',
    billCycle: '',
  })
  const [updatePlanLoading, setUpdatePlanLoading] = useState(false)
  const [updateResult, setUpdateResult] = useState<{
    username: string
    plan: string
    next_billing_date: string
  } | null>(null)
  const [showConfirmDialog, setShowConfirmDialog] = useState(false)
  const [addMessage, setAddMessage] = useState({
    username: '',
    message_count: 100,
  })
  const [addMessageLoading, setAddMessageLoading] = useState(false)
  const [addMessageResult, setAddMessageResult] = useState<{
    username: string
    previous_usage_limit: number
    new_usage_limit: number
    messages_added: number
  } | null>(null)
  const [showAddMessageConfirmDialog, setShowAddMessageConfirmDialog] = useState(false)
  const [currentUsageLimit, setCurrentUsageLimit] = useState(0)
  const [activeTab, setActiveTab] = useState('user-details')
  
  // User Details state
  const [searchUsername, setSearchUsername] = useState('')
  const [userDetails, setUserDetails] = useState<Record<string, any> | null>(null)
  const [searchLoading, setSearchLoading] = useState(false)
  const [editMode, setEditMode] = useState(false)
  const [editData, setEditData] = useState<Record<string, any> | null>(null)
  const [saveLoading, setSaveLoading] = useState(false)


  const handleUpdatePlanSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!updatePlan.username || !updatePlan.plan || !updatePlan.billCycle) {
      toast.error('Please fill in all fields')
      return
    }
    
    // Show confirmation dialog
    setShowConfirmDialog(true)
  }

  const handleConfirmUpdate = async () => {
    setUpdatePlanLoading(true)
    setUpdateResult(null)
    setShowConfirmDialog(false)

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'update_plan',
          username: updatePlan.username,
          plan: updatePlan.plan,
          billCycle: updatePlan.billCycle,
        }),
      })

      const result = await response.json()

      if (result.success && result.data) {
        toast.success('Plan updated successfully!')
        setUpdateResult(result.data)
        setUpdatePlan({ username: '', plan: '', billCycle: '' })
      } else {
        toast.error(result.error || 'Failed to update plan')
      }
    } catch (error) {
      toast.error('An error occurred while updating the plan')
    } finally {
      setUpdatePlanLoading(false)
    }
  }

  const handleAddMessageSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    // Validate form
    if (!addMessage.username || !addMessage.message_count) {
      toast.error('Please fill in all fields')
      return
    }
    
    // First, we need to get the current usage limit to show in confirmation dialog
    // For now, we'll set a placeholder - in a real implementation, you might want to fetch this first
    setCurrentUsageLimit(0) // This would be fetched from the API
    setShowAddMessageConfirmDialog(true)
  }

  const handleConfirmAddMessage = async () => {
    setAddMessageLoading(true)
    setAddMessageResult(null)
    setShowAddMessageConfirmDialog(false)

    try {
      const response = await fetch('/api/users', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          action: 'add_message',
          username: addMessage.username,
          message_count: addMessage.message_count,
        }),
      })

      const result = await response.json()

      if (result.success && result.data) {
        toast.success('Messages added successfully!')
        setAddMessageResult(result.data)
        setAddMessage({ username: '', message_count: 100 })
      } else {
        toast.error(result.error || 'Failed to add messages')
      }
    } catch (error) {
      toast.error('An error occurred while adding messages')
    } finally {
      setAddMessageLoading(false)
    }
  }

  const handleSearchUser = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!searchUsername.trim()) {
      toast.error('Please enter a username')
      return
    }

    setSearchLoading(true)
    setUserDetails(null)
    setEditMode(false)

    try {
      const response = await fetch(`/api/users/details?username=${encodeURIComponent(searchUsername)}`)
      const result = await response.json()

      if (response.ok && result.success && result.data && Object.keys(result.data).length > 0) {
        setUserDetails(result.data)
        setEditData(result.data)
      } else {
        toast.error(result.error || 'User not found')
        setUserDetails(null)
        setEditData(null)
      }
    } catch (error) {
      toast.error('An error occurred while searching for the user')
    } finally {
      setSearchLoading(false)
    }
  }

  const handleEditToggle = () => {
    if (editMode) {
      // Cancel edit - reset data
      setEditData(userDetails)
      setEditMode(false)
    } else {
      // Enter edit mode
      setEditMode(true)
    }
  }

  const handleSaveUserDetails = async () => {
    if (!userDetails || !editData) {
      toast.error('No user data to save')
      return
    }

    setSaveLoading(true)

    try {
      const response = await fetch('/api/users/details', {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          username: userDetails.username,
          company_name: editData.company_name,
          contact_email: editData.contact_email,
          contact_phone: editData.contact_phone,
          status: editData.status,
          sector: editData.sector,
          lang: editData.lang,
          lang_2: editData.lang_2,
          usage_used: editData.usage_used,
          usage_limit: editData.usage_limit,
          contact_need: editData.contact_need,
        }),
      })

      const result = await response.json()

      if (response.ok && result.success) {
        toast.success('User details updated successfully!')
        setUserDetails(result.data)
        setEditData(result.data)
        setEditMode(false)
      } else {
        toast.error(result.error || 'Failed to update user details')
      }
    } catch (error) {
      toast.error('An error occurred while updating user details')
    } finally {
      setSaveLoading(false)
    }
  }

  return (
    <div className="space-y-6">
      <div>
        <h1 className="text-3xl font-bold">User Management</h1>
        <p className="text-muted-foreground">
          Add new users and manage existing user accounts
        </p>
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid w-full grid-cols-4">
          <TabsTrigger value="user-details" className="flex items-center gap-2">
            <User className="h-4 w-4" />
            User Details
          </TabsTrigger>
          <TabsTrigger value="add-message" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Add Message
          </TabsTrigger>
          <TabsTrigger value="update-plan" className="flex items-center gap-2">
            <Crown className="h-4 w-4" />
            Update Plan
          </TabsTrigger>
          <TabsTrigger value="reset-password" className="flex items-center gap-2">
            <KeyRound className="h-4 w-4" />
            Reset Password
          </TabsTrigger>
        </TabsList>


        <TabsContent value="user-details" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <User className="h-5 w-5" />
                User Details
              </CardTitle>
              <CardDescription>
                Search for users and view/edit their details
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleSearchUser} className="space-y-4 mb-6">
                <div className="flex gap-2">
                  <div className="flex-1">
                    <Label htmlFor="search-username">Username</Label>
                    <Input
                      id="search-username"
                      type="text"
                      value={searchUsername}
                      onChange={(e) => setSearchUsername(e.target.value)}
                      placeholder="Enter username to search"
                      disabled={searchLoading}
                    />
                  </div>
                  <div className="flex items-end">
                    <Button type="submit" disabled={searchLoading}>
                      {searchLoading ? 'Searching...' : 'Search'}
                    </Button>
                  </div>
                </div>
              </form>

              {userDetails && (
                <div className="space-y-4">
                  <div className="flex justify-between items-center">
                    <h3 className="text-lg font-semibold">User Information</h3>
                    <div className="space-x-2">
                      {editMode ? (
                        <>
                          <Button
                            variant="outline"
                            onClick={handleEditToggle}
                            disabled={saveLoading}
                          >
                            Cancel
                          </Button>
                          <Button
                            onClick={handleSaveUserDetails}
                            disabled={saveLoading}
                          >
                            {saveLoading ? 'Saving...' : 'Save Changes'}
                          </Button>
                        </>
                      ) : (
                        <Button onClick={handleEditToggle}>
                          Edit
                        </Button>
                      )}
                    </div>
                  </div>

                  <div className="space-y-6">
                    {/* Account Information - Non-editable */}
                    <div>
                      <h4 className="text-md font-medium mb-3 text-muted-foreground">Account Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Auth ID</Label>
                          <Input value={userDetails.auth_id || 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Client ID</Label>
                          <Input value={userDetails.client_id || 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Username</Label>
                          <Input value={userDetails.username || 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Created At</Label>
                          <Input value={userDetails.created_at ? formatTimestamp(userDetails.created_at) : 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Updated At</Label>
                          <Input value={userDetails.updated_at ? formatTimestamp(userDetails.updated_at) : 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Created By</Label>
                          <Input value={userDetails.created_by || 'N/A'} disabled />
                        </div>
                      </div>
                    </div>

                    {/* Subscription Information - Non-editable */}
                    <div>
                      <h4 className="text-md font-medium mb-3 text-muted-foreground">Subscription Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Plan Type</Label>
                          <Input value={userDetails.plan_type || 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Billing Cycle</Label>
                          <Input value={userDetails.billing_cycle || 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Start Date</Label>
                          <Input value={userDetails.start_date ? formatDate(userDetails.start_date) : 'N/A'} disabled />
                        </div>

                        <div className="space-y-2">
                          <Label>Next Billing Date</Label>
                          <Input value={userDetails.next_billing_date ? formatDate(userDetails.next_billing_date) : 'N/A'} disabled />
                        </div>
                      </div>
                    </div>

                    {/* Contact Information - Editable */}
                    <div>
                      <h4 className="text-md font-medium mb-3 text-muted-foreground">Contact Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Company Name</Label>
                          {editMode ? (
                            <Input
                              value={editData?.company_name || ''}
                              onChange={(e) => setEditData({ ...editData, company_name: e.target.value })}
                              placeholder="Enter company name"
                            />
                          ) : (
                            <Input value={userDetails.company_name || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Contact Email</Label>
                          {editMode ? (
                            <Input
                              type="email"
                              value={editData?.contact_email || ''}
                              onChange={(e) => setEditData({ ...editData, contact_email: e.target.value })}
                              placeholder="Enter contact email"
                            />
                          ) : (
                            <Input value={userDetails.contact_email || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Contact Phone</Label>
                          {editMode ? (
                            <Input
                              value={editData?.contact_phone || ''}
                              onChange={(e) => setEditData({ ...editData, contact_phone: e.target.value })}
                              placeholder="Enter contact phone"
                            />
                          ) : (
                            <Input value={userDetails.contact_phone || 'N/A'} disabled />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Business Information - Editable */}
                    <div>
                      <h4 className="text-md font-medium mb-3 text-muted-foreground">Business Information</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Status</Label>
                          {editMode ? (
                            <Select
                              value={editData?.status || ''}
                              onValueChange={(value) => setEditData({ ...editData, status: value })}
                            >
                              <SelectTrigger>
                                <SelectValue placeholder="Select status" />
                              </SelectTrigger>
                              <SelectContent>
                                <SelectItem value="active">Active</SelectItem>
                                <SelectItem value="inactive">Inactive</SelectItem>
                                <SelectItem value="suspended">Suspended</SelectItem>
                                <SelectItem value="pending">Pending</SelectItem>
                              </SelectContent>
                            </Select>
                          ) : (
                            <Input value={userDetails.status || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Sector</Label>
                          {editMode ? (
                            <Input
                              value={editData?.sector || ''}
                              onChange={(e) => setEditData({ ...editData, sector: e.target.value })}
                              placeholder="Enter business sector"
                            />
                          ) : (
                            <Input value={userDetails.sector || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Contact Need</Label>
                          {editMode ? (
                            <Input
                              value={editData?.contact_need || ''}
                              onChange={(e) => setEditData({ ...editData, contact_need: e.target.value })}
                              placeholder="Enter contact need"
                            />
                          ) : (
                            <Input value={userDetails.contact_need || 'N/A'} disabled />
                          )}
                        </div>
                      </div>
                    </div>

                    {/* Language & Usage Information - Editable */}
                    <div>
                      <h4 className="text-md font-medium mb-3 text-muted-foreground">Language & Usage</h4>
                      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                        <div className="space-y-2">
                          <Label>Primary Language</Label>
                          {editMode ? (
                            <Input
                              value={editData?.lang || ''}
                              onChange={(e) => setEditData({ ...editData, lang: e.target.value })}
                              placeholder="Enter primary language"
                            />
                          ) : (
                            <Input value={userDetails.lang || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Secondary Language</Label>
                          {editMode ? (
                            <Input
                              value={editData?.lang_2 || ''}
                              onChange={(e) => setEditData({ ...editData, lang_2: e.target.value })}
                              placeholder="Enter secondary language"
                            />
                          ) : (
                            <Input value={userDetails.lang_2 || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Usage Used</Label>
                          {editMode ? (
                            <Input
                              type="number"
                              value={editData?.usage_used || ''}
                              onChange={(e) => setEditData({ ...editData, usage_used: parseInt(e.target.value) || null })}
                              placeholder="Enter usage used"
                            />
                          ) : (
                            <Input value={userDetails.usage_used?.toLocaleString() || 'N/A'} disabled />
                          )}
                        </div>

                        <div className="space-y-2">
                          <Label>Usage Limit</Label>
                          {editMode ? (
                            <Input
                              type="number"
                              value={editData?.usage_limit || ''}
                              onChange={(e) => setEditData({ ...editData, usage_limit: parseInt(e.target.value) || null })}
                              placeholder="Enter usage limit"
                            />
                          ) : (
                            <Input value={userDetails.usage_limit?.toLocaleString() || 'N/A'} disabled />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="add-message" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Add Messages
              </CardTitle>
              <CardDescription>
                Add messages to user accounts
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleAddMessageSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username-add-message">Username</Label>
                  <Input
                    id="username-add-message"
                    type="text"
                    value={addMessage.username}
                    onChange={(e) => setAddMessage({ ...addMessage, username: e.target.value })}
                    placeholder="Enter username"
                    required
                    disabled={addMessageLoading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message-count">Number of Messages</Label>
                  <Select
                    value={addMessage.message_count.toString()}
                    onValueChange={(value) => setAddMessage({ ...addMessage, message_count: parseInt(value) })}
                    disabled={addMessageLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select message count" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="100">100</SelectItem>
                      <SelectItem value="500">500</SelectItem>
                      <SelectItem value="1000">1,000</SelectItem>
                      <SelectItem value="2000">2,000</SelectItem>
                      <SelectItem value="5000">5,000</SelectItem>
                      <SelectItem value="10000">10,000</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button type="submit" className="w-full" disabled={addMessageLoading}>
                  {addMessageLoading ? 'Adding Messages...' : 'Add Messages'}
                </Button>
              </form>

              {addMessageResult && (
                <div className="mt-6 p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-500">
                  <h3 className="font-semibold mb-2">Messages Added Successfully</h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Username:</strong> {addMessageResult.username}</p>
                    <p><strong>Messages Added:</strong> {addMessageResult.messages_added.toLocaleString()}</p>
                  </div>
                </div>
              )}

              <AddMessageConfirmation
                open={showAddMessageConfirmDialog}
                onOpenChange={setShowAddMessageConfirmDialog}
                username={addMessage.username}
                messageCount={addMessage.message_count}
                currentUsageLimit={currentUsageLimit}
                onConfirm={handleConfirmAddMessage}
                loading={addMessageLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="update-plan" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Crown className="h-5 w-5" />
                Update Plan
              </CardTitle>
              <CardDescription>
                Update user subscription plans and billing cycles
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form onSubmit={handleUpdatePlanSubmit} className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="username">Username</Label>
                  <Input
                    id="username"
                    type="text"
                    value={updatePlan.username}
                    onChange={(e) => setUpdatePlan({ ...updatePlan, username: e.target.value })}
                    placeholder="Enter username"
                    required
                    disabled={updatePlanLoading}
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="plan-update">Plan Name</Label>
                  <Select
                    value={updatePlan.plan}
                    onValueChange={(value) => setUpdatePlan({ ...updatePlan, plan: value })}
                    disabled={updatePlanLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select a plan" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Intern">Intern</SelectItem>
                      <SelectItem value="Assistant">Assistant</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="bill-cycle">Bill Cycle</Label>
                  <Select
                    value={updatePlan.billCycle}
                    onValueChange={(value) => setUpdatePlan({ ...updatePlan, billCycle: value })}
                    disabled={updatePlanLoading}
                  >
                    <SelectTrigger>
                      <SelectValue placeholder="Select billing cycle" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1 month">1 Month</SelectItem>
                      <SelectItem value="3 months">3 Months</SelectItem>
                      <SelectItem value="6 months">6 Months</SelectItem>
                      <SelectItem value="1 year">1 Year</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <Button type="submit" className="w-full" disabled={updatePlanLoading}>
                  {updatePlanLoading ? 'Updating Plan...' : 'Update Plan'}
                </Button>
              </form>

              {updateResult && (
                <div className="mt-6 p-4 rounded-lg bg-green-500/10 border border-green-500/20 text-green-500">
                  <h3 className="font-semibold mb-2">Plan Updated Successfully</h3>
                  <div className="space-y-1 text-sm">
                    <p><strong>Username:</strong> {updateResult.username}</p>
                    <p><strong>Plan:</strong> {updateResult.plan}</p>
                    <p><strong>Next Billing Date:</strong> {updateResult.next_billing_date ? formatDate(updateResult.next_billing_date) : 'N/A'}</p>
                  </div>
                </div>
              )}

              <PlanUpdateConfirmation
                open={showConfirmDialog}
                onOpenChange={setShowConfirmDialog}
                username={updatePlan.username}
                newPlan={updatePlan.plan}
                billingCycle={updatePlan.billCycle}
                onConfirm={handleConfirmUpdate}
                loading={updatePlanLoading}
              />
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="reset-password" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <KeyRound className="h-5 w-5" />
                Reset Password
              </CardTitle>
              <CardDescription>
                Reset user passwords and generate new credentials
              </CardDescription>
            </CardHeader>
            <CardContent>
              <ResetPasswordForm />
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}