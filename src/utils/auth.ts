import { createServiceRoleClient } from '@/lib/supabase'
import { NextRequest } from 'next/server'

export async function verifyAdminAuth(request: NextRequest) {
  try {
    // Get the session token from the Authorization header
    const authHeader = request.headers.get('authorization')
    const sessionToken = authHeader?.replace('Bearer ', '')

    if (!sessionToken) {
      console.log('No session token found in Authorization header')
      return { authenticated: false, isAdmin: false }
    }

    // Use service role client to verify the session
    const supabase = createServiceRoleClient()
    
    const { data: { user }, error } = await supabase.auth.getUser(sessionToken)
    
    if (error || !user || !user.email) {
      console.log('User verification failed:', error?.message || 'No user found')
      return { authenticated: false, isAdmin: false }
    }

    // Check if user email matches ADMIN_USER from env
    const adminEmail = process.env.ADMIN_USER
    
    if (!adminEmail || user.email !== adminEmail) {
      console.log('Admin access denied: email mismatch')
      return { authenticated: true, isAdmin: false }
    }

    return { authenticated: true, isAdmin: true }
  } catch (error) {
    console.error('Auth verification error:', error)
    return { authenticated: false, isAdmin: false }
  }
}