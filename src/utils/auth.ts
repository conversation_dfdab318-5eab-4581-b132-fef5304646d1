import { jwtVerify, createRemoteJWKSet } from 'jose'
import { createServiceRoleClient } from '@/lib/supabase'
import { NextRequest } from 'next/server'

// JWKS cache to avoid repeated network calls
const jwksCache = new Map<string, { jwks: any; timestamp: number }>()
const JWKS_CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

interface AuthResult {
  authenticated: boolean
  isAdmin: boolean
  user?: any
  method?: 'DIRECT_JWT' | 'GET_CLAIMS' | 'GET_USER'
  duration?: number // in seconds
}

export async function verifyAuth(request: NextRequest): Promise<AuthResult> {
  const startTime = Date.now()

  try {
    // Get the session token from the Authorization header
    const authHeader = request.headers.get('authorization')
    const sessionToken = authHeader?.replace('Bearer ', '')

    if (!sessionToken) {
      return {
        authenticated: false,
        isAdmin: false,
        method: 'GET_USER',
        duration: (Date.now() - startTime) / 1000
      }
    }

    // Strategy 1: Direct JWT verification (fastest)
    try {
      const result = await verifyJWTDirect(sessionToken)

      if (result.success && result.user) {
        const isAdmin = await checkAdminStatus(result.user)
        const duration = (Date.now() - startTime) / 1000

        console.log(`[AUTH] DIRECT_JWT ${duration.toFixed(3)}s - ${result.user.email}`)

        return {
          authenticated: true,
          isAdmin,
          user: result.user,
          method: 'DIRECT_JWT',
          duration
        }
      }
    } catch (error) {
      // Silent fallback
    }

    // Strategy 2: getClaims() fallback
    try {
      const supabase = createServiceRoleClient()
      const claimsResponse = await supabase.auth.getClaims(sessionToken)

      if (claimsResponse && !claimsResponse.error && claimsResponse.data?.claims) {
        const claims = claimsResponse.data.claims
        const user = {
          id: claims.sub,
          email: claims.email,
          app_metadata: (claims as any).app_metadata || {}
        }
        const isAdmin = await checkAdminStatus(user)
        const duration = (Date.now() - startTime) / 1000

        console.log(`[AUTH] GET_CLAIMS ${duration.toFixed(3)}s - ${user.email}`)

        return {
          authenticated: true,
          isAdmin,
          user,
          method: 'GET_CLAIMS',
          duration
        }
      }
    } catch (error) {
      // Silent fallback
    }

    // Strategy 3: getUser() fallback (slowest)
    try {
      const supabase = createServiceRoleClient()
      const { data: { user }, error } = await supabase.auth.getUser(sessionToken)

      if (error || !user || !user.email) {
        return {
          authenticated: false,
          isAdmin: false,
          method: 'GET_USER',
          duration: (Date.now() - startTime) / 1000
        }
      }

      const isAdmin = await checkAdminStatus(user)
      const duration = (Date.now() - startTime) / 1000

      console.log(`[AUTH] GET_USER ${duration.toFixed(3)}s - ${user.email}`)

      return {
        authenticated: true,
        isAdmin,
        user,
        method: 'GET_USER',
        duration
      }
    } catch (error) {
      return {
        authenticated: false,
        isAdmin: false,
        method: 'GET_USER',
        duration: (Date.now() - startTime) / 1000
      }
    }
  } catch (error) {
    return {
      authenticated: false,
      isAdmin: false,
      method: 'GET_USER',
      duration: (Date.now() - startTime) / 1000
    }
  }
}

/**
 * Direct JWT verification using JOSE with JWKS caching
 */
async function verifyJWTDirect(token: string): Promise<{ success: boolean; user?: any }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const jwksUrl = `${supabaseUrl}/auth/v1/.well-known/jwks.json`

    // Check cache first
    const cached = jwksCache.get(jwksUrl)
    const now = Date.now()

    let jwks
    if (cached && (now - cached.timestamp) < JWKS_CACHE_DURATION) {
      jwks = cached.jwks
    } else {
      // Create new JWKS and cache it
      jwks = createRemoteJWKSet(new URL(jwksUrl))
      jwksCache.set(jwksUrl, { jwks, timestamp: now })
    }

    const { payload } = await jwtVerify(token, jwks)

    return {
      success: true,
      user: {
        id: payload.sub,
        email: payload.email,
        app_metadata: payload.app_metadata || {}
      }
    }
  } catch (error) {
    return { success: false }
  }
}

/**
 * Check if user has admin status
 */
async function checkAdminStatus(user: any): Promise<boolean> {
  // Check if user has admin role in metadata
  const userRole = user.app_metadata?.role
  if (userRole === 'admin') {
    return true
  }

  // Check if user email matches ADMIN_USER from env
  const adminEmail = process.env.ADMIN_USER
  if (adminEmail && user.email === adminEmail) {
    return true
  }

  return false
}

export async function verifyAdminAuth(request: NextRequest) {
  const result = await verifyAuth(request)
  return {
    authenticated: result.authenticated,
    isAdmin: result.isAdmin
  }
}