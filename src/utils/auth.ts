import { jwtVerify, createRemoteJWKSet } from 'jose'
import { createServiceRoleClient } from '@/lib/supabase'
import { NextRequest } from 'next/server'

// JWKS cache to avoid repeated network calls
const jwksCache = new Map<string, { jwks: any; timestamp: number }>()
const JWKS_CACHE_DURATION = 10 * 60 * 1000 // 10 minutes

interface AuthResult {
  authenticated: boolean
  isAdmin: boolean
  user?: any
  method?: 'DIRECT_JWT_VERIFY' | 'GET_CLAIMS_FALLBACK' | 'GET_USER_FALLBACK'
  timings?: {
    directVerification?: number
    getClaims?: number
    getUser?: number
    total: number
  }
}

/**
 * Optimized authentication verification using new Supabase JWT signing keys
 * Implements three-tier fallback strategy for maximum performance
 */
export async function verifyAuth(request: NextRequest): Promise<AuthResult> {
  const startTime = Date.now()
  const timings: AuthResult['timings'] = { total: 0 }

  try {
    // Get the session token from the Authorization header or cookies
    const authHeader = request.headers.get('authorization')
    let sessionToken = authHeader?.replace('Bearer ', '')

    if (!sessionToken) {
      // Try to get from cookies as fallback
      const cookies = request.cookies
      const accessToken = cookies.get('sb-access-token')?.value
      sessionToken = accessToken
    }

    if (!sessionToken) {
      console.log('[AUTH] No session token found')
      return {
        authenticated: false,
        isAdmin: false,
        method: 'GET_USER_FALLBACK',
        timings: { ...timings, total: Date.now() - startTime }
      }
    }

    // Strategy 1: Direct JWT verification (fastest ~5-15ms after JWKS cache)
    try {
      const directStart = Date.now()
      const result = await verifyJWTDirect(sessionToken)
      timings.directVerification = Date.now() - directStart

      if (result.success && result.user) {
        const isAdmin = await checkAdminStatus(result.user)
        timings.total = Date.now() - startTime

        console.log(`[AUTH] ✅ Direct JWT verification successful:`)
        console.log(`[AUTH] - Direct verification: ${timings.directVerification}ms`)
        console.log(`[AUTH] - Total auth time: ${timings.total}ms`)
        console.log(`[AUTH] - Method: DIRECT_JWT_VERIFY (fastest)`)
        console.log(`[AUTH] - User: ${result.user.email}, Admin: ${isAdmin}`)

        return {
          authenticated: true,
          isAdmin,
          user: result.user,
          method: 'DIRECT_JWT_VERIFY',
          timings
        }
      }
    } catch (error) {
      console.log('[AUTH] ⚠️ Direct JWT verification failed, falling back to getClaims:', error)
    }

    // Strategy 2: getClaims() fallback (~100-150ms)
    try {
      const claimsStart = Date.now()
      const supabase = createServiceRoleClient()
      const claimsResponse = await supabase.auth.getClaims(sessionToken)
      timings.getClaims = Date.now() - claimsStart

      if (claimsResponse && !claimsResponse.error && claimsResponse.data?.claims) {
        const claims = claimsResponse.data.claims
        const user = {
          id: claims.sub,
          email: claims.email,
          app_metadata: (claims as any).app_metadata || {}
        }
        const isAdmin = await checkAdminStatus(user)
        timings.total = Date.now() - startTime

        console.log(`[AUTH] ✅ getClaims() verification successful:`)
        console.log(`[AUTH] - getClaims time: ${timings.getClaims}ms`)
        console.log(`[AUTH] - Total auth time: ${timings.total}ms`)
        console.log(`[AUTH] - Method: GET_CLAIMS_FALLBACK`)
        console.log(`[AUTH] - User: ${user.email}, Admin: ${isAdmin}`)

        return {
          authenticated: true,
          isAdmin,
          user,
          method: 'GET_CLAIMS_FALLBACK',
          timings
        }
      }
    } catch (error) {
      console.log('[AUTH] ⚠️ getClaims() failed, falling back to getUser:', error)
    }

    // Strategy 3: getUser() fallback (~150-200ms)
    try {
      const getUserStart = Date.now()
      const supabase = createServiceRoleClient()
      const { data: { user }, error } = await supabase.auth.getUser(sessionToken)
      timings.getUser = Date.now() - getUserStart

      if (error || !user || !user.email) {
        console.log('[AUTH] ❌ User verification failed:', error?.message || 'No user found')
        timings.total = Date.now() - startTime
        return {
          authenticated: false,
          isAdmin: false,
          method: 'GET_USER_FALLBACK',
          timings
        }
      }

      const isAdmin = await checkAdminStatus(user)
      timings.total = Date.now() - startTime

      console.log(`[AUTH] ✅ getUser() verification successful:`)
      console.log(`[AUTH] - getUser time: ${timings.getUser}ms`)
      console.log(`[AUTH] - Total auth time: ${timings.total}ms`)
      console.log(`[AUTH] - Method: GET_USER_FALLBACK (slowest)`)
      console.log(`[AUTH] - User: ${user.email}, Admin: ${isAdmin}`)

      return {
        authenticated: true,
        isAdmin,
        user,
        method: 'GET_USER_FALLBACK',
        timings
      }
    } catch (error) {
      console.error('[AUTH] ❌ All verification methods failed:', error)
      timings.total = Date.now() - startTime
      return {
        authenticated: false,
        isAdmin: false,
        method: 'GET_USER_FALLBACK',
        timings
      }
    }
  } catch (error) {
    console.error('[AUTH] ❌ Auth verification error:', error)
    timings.total = Date.now() - startTime
    return {
      authenticated: false,
      isAdmin: false,
      method: 'GET_USER_FALLBACK',
      timings
    }
  }
}

/**
 * Direct JWT verification using JOSE with JWKS caching
 */
async function verifyJWTDirect(token: string): Promise<{ success: boolean; user?: any }> {
  try {
    const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL!
    const jwksUrl = `${supabaseUrl}/auth/v1/.well-known/jwks.json`

    // Check cache first
    const cached = jwksCache.get(jwksUrl)
    const now = Date.now()

    let jwks
    if (cached && (now - cached.timestamp) < JWKS_CACHE_DURATION) {
      jwks = cached.jwks
    } else {
      // Create new JWKS and cache it
      jwks = createRemoteJWKSet(new URL(jwksUrl))
      jwksCache.set(jwksUrl, { jwks, timestamp: now })
    }

    const { payload } = await jwtVerify(token, jwks)

    return {
      success: true,
      user: {
        id: payload.sub,
        email: payload.email,
        app_metadata: payload.app_metadata || {}
      }
    }
  } catch (error) {
    return { success: false }
  }
}

/**
 * Check if user has admin status
 */
async function checkAdminStatus(user: any): Promise<boolean> {
  // Check if user has admin role in metadata
  const userRole = user.app_metadata?.role
  if (userRole === 'admin') {
    return true
  }

  // Check if user email matches ADMIN_USER from env
  const adminEmail = process.env.ADMIN_USER
  if (adminEmail && user.email === adminEmail) {
    return true
  }

  return false
}

/**
 * Legacy compatibility function - use verifyAuth for better performance
 */
export async function verifyAdminAuth(request: NextRequest) {
  const result = await verifyAuth(request)
  return {
    authenticated: result.authenticated,
    isAdmin: result.isAdmin
  }
}