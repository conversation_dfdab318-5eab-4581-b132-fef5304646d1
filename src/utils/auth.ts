import { verifyAuth } from '@/lib/auth-optimized'
import { NextRequest } from 'next/server'

/**
 * Legacy compatibility function - use verifyAuth from @/lib/auth-optimized for better performance
 * This function is kept for backward compatibility with existing code
 */
export async function verifyAdminAuth(request: NextRequest) {
  const result = await verifyAuth(request)
  return {
    authenticated: result.authenticated,
    isAdmin: result.isAdmin
  }
}