'use client'

import { usePathname } from 'next/navigation'
import { AdminSidebar } from '@/components/layout/admin-sidebar'
import { SidebarProvider, SidebarTrigger } from '@/components/ui/sidebar'
import { useAuth } from '@/components/providers/auth-provider'

export function DashboardLayout({ children }: { children: React.ReactNode }) {
  const pathname = usePathname()
  const { user } = useAuth()

  // Don't show sidebar on login page or if user is not authenticated
  const isLoginPage = pathname === '/login'
  const showSidebar = user && !isLoginPage

  if (!showSidebar) {
    return <>{children}</>
  }

  return (
    <SidebarProvider defaultOpen={false}>
      <AdminSidebar />
      <main className="flex-1 overflow-auto">
        <div className="container mx-auto p-6">
          <div className="mb-4">
            <SidebarTrigger />
          </div>
          {children}
        </div>
      </main>
    </SidebarProvider>
  )
}